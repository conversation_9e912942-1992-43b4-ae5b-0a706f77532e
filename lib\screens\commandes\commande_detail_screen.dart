import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:intl/intl.dart';
import '../../models/commande.dart';
import '../../models/client.dart';
import '../../providers/firebase_client_provider.dart';
import '../../providers/commande_provider.dart';
import '../../services/whatsapp_service.dart';
import '../../widgets/whatsapp_message_dialog.dart';
import '../../widgets/professional_ui_components.dart';
import 'facture_screen.dart';

class CommandeDetailScreen extends StatelessWidget {
  final Commande commande;

  const CommandeDetailScreen({super.key, required this.commande});

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 480;
    final padding = isSmallScreen ? 16.0 : 20.0;

    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      appBar: AppBar(
        title: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Commande #${commande.id}',
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: Color(0xFF1F2937),
              ),
            ),
            Text(
              'Détails de la commande',
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w400,
                color: Colors.grey.shade600,
              ),
            ),
          ],
        ),
        elevation: 0,
        backgroundColor: Colors.white,
        foregroundColor: const Color(0xFF1F2937),
        toolbarHeight: 70,
        actions: [
          IconButton(
            icon: const Icon(Icons.receipt),
            onPressed: () => _naviguerVersFacture(context),
            tooltip: 'Générer facture',
          ),
          if (commande.statut != StatutCommande.annulee &&
              commande.statut != StatutCommande.livree)
            PopupMenuButton<String>(
              onSelected: (value) => _gererAction(context, value),
              icon: const Icon(Icons.more_vert),
              tooltip: 'Plus d\'options',
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'whatsapp',
                  child: Row(
                    children: [
                      Icon(Icons.chat, color: Color(0xFF25D366), size: 18),
                      SizedBox(width: 8),
                      Text('Envoyer par WhatsApp'),
                    ],
                  ),
                ),
                const PopupMenuItem(
                  value: 'modifier_statut',
                  child: Row(
                    children: [
                      Icon(Icons.edit, size: 18),
                      SizedBox(width: 8),
                      Text('Modifier statut'),
                    ],
                  ),
                ),
                if (commande.statut == StatutCommande.enAttente)
                  const PopupMenuItem(
                    value: 'annuler',
                    child: Row(
                      children: [
                        Icon(Icons.cancel, color: Colors.red, size: 18),
                        SizedBox(width: 8),
                        Text(
                          'Annuler',
                          style: TextStyle(color: Colors.red),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
          const SizedBox(width: 8),
        ],
      ),
      body: Consumer<FirebaseClientProvider>(
        builder: (context, clientProvider, child) {
          final client = clientProvider.obtenirClientParId(commande.clientId);

          if (client == null) {
            return const Center(child: Text('Client introuvable'));
          }

          final screenWidth = MediaQuery.of(context).size.width;
          final isSmallScreen = screenWidth < 480;
          final padding = isSmallScreen ? 16.0 : 20.0;

          return SingleChildScrollView(
            padding: EdgeInsets.all(padding),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildInfosGenerales(context, client),
                const SizedBox(height: 20),
                _buildStatutCard(context),
                const SizedBox(height: 20),
                _buildItemsCard(context),
                const SizedBox(height: 20),
                _buildTotalCard(context),
                if (commande.notes != null) ...[
                  const SizedBox(height: 20),
                  _buildNotesCard(context),
                ],
                const SizedBox(height: 20), // Extra bottom padding
              ],
            ),
          );
        },
      ),
    );
  }

  Widget _buildInfosGenerales(BuildContext context, Client client) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.info_outline,
                    color: Color(0xFF6366F1),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Informations générales',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (isSmallScreen)
              // Version mobile - colonnes empilées
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildClientInfoCard(context, client),
                  const SizedBox(height: 16),
                  _buildCommandeInfoCard(context),
                ],
              )
            else
              // Version desktop - côte à côte
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: _buildClientInfoCard(context, client)),
                  const SizedBox(width: 20),
                  Expanded(child: _buildCommandeInfoCard(context)),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildClientInfoCard(BuildContext context, Client client) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [Colors.blue.shade50, Colors.indigo.shade50],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.blue.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.person_outline,
                  color: Colors.blue,
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Informations Client',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: Colors.blue.shade700,
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),

          // Nom et Code client
          _buildInfoRow('Nom client:', client.nomComplet, isImportant: true),
          if (client.codeClient?.isNotEmpty == true)
            _buildInfoRow('Code client:', client.codeClient!),

          // Catégorie
          if (client.categorie?.isNotEmpty == true)
            _buildInfoRow('Catégorie:', client.categorie!),

          // Matricule fiscal
          if (client.matriculeFiscale?.isNotEmpty == true)
            _buildInfoRow('Matricule fiscal:', client.matriculeFiscale!),

          // Contact
          _buildInfoRow('Tél:', client.primaryPhone),
          _buildInfoRow('Email:', client.email),

          // Adresse
          if (client.adresse.isNotEmpty)
            _buildInfoRow('Adresse:', client.adresse),

          // Mode de règlement
          if (client.modeReglement?.isNotEmpty == true)
            _buildInfoRow('Mode de règlement:', client.modeReglement!),
        ],
      ),
    );
  }

  Widget _buildCommandeInfoCard(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            const Color(0xFF6366F1).withValues(alpha: 0.1),
            const Color(0xFF8B5CF6).withValues(alpha: 0.1),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: const Color(0xFF6366F1).withValues(alpha: 0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.receipt_long_outlined,
                  color: Color(0xFF6366F1),
                  size: 16,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Détails Commande',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF6366F1),
                    ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoRow('N° Commande:', '#${commande.id}', isImportant: true),
          _buildInfoRow(
            'Date:',
            DateFormat('dd/MM/yyyy à HH:mm').format(commande.dateCommande),
          ),
          _buildInfoRow('Articles:', '${commande.nombreArticles} article(s)'),
          _buildInfoRow(
            'Montant total HT:',
            commande.montantFormate,
            isImportant: true,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoRow(String label, String value, {bool isImportant = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 130,
            child: Text(
              label,
              style: TextStyle(
                fontSize: 13,
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                fontSize: 14,
                fontWeight: isImportant ? FontWeight.w700 : FontWeight.w500,
                color: isImportant
                    ? const Color(0xFF1F2937)
                    : const Color(0xFF374151),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatutCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: _getStatutColor(
                      commande.statut,
                    ).withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _getStatutIcon(commande.statut),
                    color: _getStatutColor(commande.statut),
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Statut de la commande',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            StatusBadge(
              text: commande.statutFormate,
              backgroundColor: _getStatutColor(
                commande.statut,
              ).withValues(alpha: 0.1),
              textColor: _getStatutColor(commande.statut),
              icon: _getStatutIcon(commande.statut),
              fontSize: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildItemsCard(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isSmallScreen = screenWidth < 600;

    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.shopping_cart_outlined,
                    color: Colors.orange,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Articles commandés',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            if (isSmallScreen)
              // Version mobile - cartes pour chaque article
              ...commande.items.map(
                (item) => _buildMobileItemCard(context, item),
              )
            else
              // Version desktop - tableau
              _buildDesktopItemsTable(context),
          ],
        ),
      ),
    );
  }

  Widget _buildMobileItemCard(BuildContext context, item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: Colors.grey.shade200),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            blurRadius: 12,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Expanded(
                child: Text(
                  item.nomProduit,
                  style: const TextStyle(
                    fontWeight: FontWeight.w700,
                    fontSize: 15,
                    color: Color(0xFF1F2937),
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 10,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  item.referenceFormatee,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF6366F1),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Quantité',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      '${item.quantite} ${item.uniteFormatee}',
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Prix unitaire HT',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.prixUnitaireFormate,
                      style: const TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Color(0xFF374151),
                      ),
                    ),
                  ],
                ),
              ),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      'Total HT',
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey[600],
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      item.sousTotalFormate,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: Color(0xFF6366F1),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildDesktopItemsTable(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SizedBox(
        width: MediaQuery.of(context).size.width - 64,
        child: Table(
          border: TableBorder.all(
            color: Colors.grey.shade300,
            borderRadius: BorderRadius.circular(8),
          ),
          columnWidths: const {
            0: FlexColumnWidth(1.5), // Référence
            1: FlexColumnWidth(3), // Désignation
            2: FlexColumnWidth(1.2), // Quantité
            3: FlexColumnWidth(1), // Unité
            4: FlexColumnWidth(1.5), // Prix unitaire HT
            5: FlexColumnWidth(1.5), // Total HT
          },
          children: [
            // En-tête du tableau
            TableRow(
              decoration: BoxDecoration(
                color: Theme.of(
                  context,
                ).colorScheme.primary.withValues(alpha: 0.1),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              children: [
                _buildTableHeader('Référence'),
                _buildTableHeader('Désignation'),
                _buildTableHeader('Quantité'),
                _buildTableHeader('Unité'),
                _buildTableHeader('Prix unitaire HT'),
                _buildTableHeader('Total HT'),
              ],
            ),
            // Lignes de données
            ...commande.items.map(
              (item) => TableRow(
                children: [
                  _buildTableCell(item.referenceFormatee, isCode: true),
                  _buildTableCell(item.nomProduit, isBold: true),
                  _buildTableCell(item.quantite.toString(), isCenter: true),
                  _buildTableCell(item.uniteFormatee, isCenter: true),
                  _buildTableCell(item.prixUnitaireFormate, isRight: true),
                  _buildTableCell(
                    item.sousTotalFormate,
                    isRight: true,
                    isBold: true,
                    isAmount: true,
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTableHeader(String text) {
    return Padding(
      padding: const EdgeInsets.all(12),
      child: Text(
        text,
        style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 14),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildTableCell(
    String text, {
    bool isCenter = false,
    bool isRight = false,
    bool isBold = false,
    bool isCode = false,
    bool isAmount = false,
  }) {
    return Builder(
      builder: (context) => Padding(
        padding: const EdgeInsets.all(12),
        child: isCode
            ? Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.grey.shade200,
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Text(
                  text,
                  style: TextStyle(
                    fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                    fontSize: 12,
                    fontFamily: 'monospace',
                  ),
                  textAlign: isCenter
                      ? TextAlign.center
                      : isRight
                          ? TextAlign.right
                          : TextAlign.left,
                ),
              )
            : Text(
                text,
                style: TextStyle(
                  fontWeight: isBold ? FontWeight.bold : FontWeight.normal,
                  fontSize: 14,
                  color:
                      isAmount ? Theme.of(context).colorScheme.primary : null,
                ),
                textAlign: isCenter
                    ? TextAlign.center
                    : isRight
                        ? TextAlign.right
                        : TextAlign.left,
              ),
      ),
    );
  }

  Widget _buildTotalCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: const Color(0xFF6366F1).withValues(alpha: 0.3),
          width: 1.5,
        ),
      ),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(24),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              const Color(0xFF6366F1).withValues(alpha: 0.05),
              const Color(0xFF8B5CF6).withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: const Color(0xFF6366F1).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.attach_money,
                color: Color(0xFF6366F1),
                size: 20,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                'Total de la commande',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: const Color(0xFF1F2937),
                    ),
              ),
            ),
            Text(
              commande.montantFormate,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: const Color(0xFF6366F1),
                    fontSize: 20,
                  ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildNotesCard(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            blurRadius: 20,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.amber.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.note_outlined,
                    color: Colors.amber,
                    size: 20,
                  ),
                ),
                const SizedBox(width: 12),
                Text(
                  'Notes',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.w700,
                        color: const Color(0xFF1F2937),
                      ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.grey.shade50,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade200),
              ),
              child: Text(
                commande.notes!,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: const Color(0xFF374151),
                      height: 1.5,
                    ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _naviguerVersFacture(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => FactureScreen(commande: commande),
      ),
    );
  }

  void _gererAction(BuildContext context, String action) {
    switch (action) {
      case 'whatsapp':
        _envoyerParWhatsApp(context);
        break;
      case 'modifier_statut':
        _afficherDialogueStatut(context);
        break;
      case 'annuler':
        _confirmerAnnulation(context);
        break;
    }
  }

  void _afficherDialogueStatut(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Modifier le statut',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: StatutCommande.values
              .where(
                (s) => s != commande.statut && s != StatutCommande.annulee,
              )
              .map(
                (statut) => ListTile(
                  title: Text(_getStatutLabel(statut)),
                  leading: CircleAvatar(
                    backgroundColor: _getStatutColor(statut),
                    radius: 8,
                  ),
                  onTap: () async {
                    Navigator.pop(context);
                    final success = await context
                        .read<CommandeProvider>()
                        .mettreAJourStatut(commande.id!, statut);
                    if (success && context.mounted) {
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(
                          content: Text(
                            'Statut mis à jour avec succès',
                          ),
                          backgroundColor: Color(0xFF10B981),
                          behavior: SnackBarBehavior.floating,
                        ),
                      );
                    }
                  },
                ),
              )
              .toList(),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Annuler'),
          ),
        ],
      ),
    );
  }

  void _confirmerAnnulation(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text(
          'Confirmer l\'annulation',
          style: TextStyle(fontWeight: FontWeight.w600),
        ),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Text(
          'Êtes-vous sûr de vouloir annuler la commande #${commande.id} ?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Non'),
          ),
          FilledButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await context
                  .read<CommandeProvider>()
                  .annulerCommande(commande.id!);
              if (success && context.mounted) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Commande annulée avec succès'),
                    backgroundColor: Color(0xFF10B981),
                    behavior: SnackBarBehavior.floating,
                  ),
                );
              }
            },
            style: FilledButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Oui, annuler'),
          ),
        ],
      ),
    );
  }

  void _envoyerParWhatsApp(BuildContext context) async {
    try {
      // Obtenir les informations du client
      if (!context.read<FirebaseClientProvider>().isLoading) {
        await context.read<FirebaseClientProvider>().loadClients();
      }
      final client = context.read<FirebaseClientProvider>().obtenirClientParId(
            commande.clientId,
          );

      if (client == null) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Client introuvable'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      if (client.primaryPhone.isEmpty) {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Numéro de téléphone du client manquant'),
              backgroundColor: Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
        return;
      }

      // Générer l'aperçu du message
      final messageParDefaut = WhatsAppService.genererMessageCommande(
            commande,
            client,
            null,
          ).split('\n').take(3).join('\n') +
          '...';

      if (context.mounted) {
        // Afficher la boîte de dialogue pour personnaliser le message
        final messagePersonnalise = await showDialog<String>(
          context: context,
          builder: (context) => WhatsAppMessageDialog(
            titre: 'Envoyer la commande par WhatsApp',
            messageParDefaut: messageParDefaut,
            client: client,
            onEnvoyer: () {},
          ),
        );

        // Vérifier si l'utilisateur n'a pas annulé
        if (messagePersonnalise == null) {
          return; // L'utilisateur a cliqué sur Annuler
        }

        // Envoyer la commande (chaîne vide = message par défaut, sinon message personnalisé)
        final messageAUtiliser =
            messagePersonnalise.isEmpty ? null : messagePersonnalise;
        final success = await WhatsAppService.envoyerCommande(
          commande: commande,
          client: client,
          messagePersonnalise: messageAUtiliser,
        );

        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                success
                    ? 'WhatsApp ouvert avec la commande'
                    : 'Erreur lors de l\'ouverture de WhatsApp',
              ),
              backgroundColor: success ? const Color(0xFF10B981) : Colors.red,
              behavior: SnackBarBehavior.floating,
            ),
          );
        }
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur: $e'),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  String _getStatutLabel(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return 'En attente';
      case StatutCommande.confirmee:
        return 'Confirmée';
      case StatutCommande.enPreparation:
        return 'En préparation';
      case StatutCommande.expediee:
        return 'Expédiée';
      case StatutCommande.livree:
        return 'Livrée';
      case StatutCommande.annulee:
        return 'Annulée';
    }
  }

  Color _getStatutColor(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return const Color(0xFFF59E0B); // Amber for waiting
      case StatutCommande.confirmee:
        return const Color(0xFF3B82F6); // Blue for confirmed
      case StatutCommande.enPreparation:
        return const Color(0xFF8B5CF6); // Purple for preparation
      case StatutCommande.expediee:
        return const Color(0xFF6366F1); // Indigo for shipped
      case StatutCommande.livree:
        return const Color(0xFF10B981); // Green for delivered
      case StatutCommande.annulee:
        return const Color(0xFFEF4444); // Red for cancelled
    }
  }

  IconData _getStatutIcon(StatutCommande statut) {
    switch (statut) {
      case StatutCommande.enAttente:
        return Icons.schedule;
      case StatutCommande.confirmee:
        return Icons.check_circle_outline;
      case StatutCommande.enPreparation:
        return Icons.construction;
      case StatutCommande.expediee:
        return Icons.local_shipping;
      case StatutCommande.livree:
        return Icons.check_circle;
      case StatutCommande.annulee:
        return Icons.cancel;
    }
  }
}
